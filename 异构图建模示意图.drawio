<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="异构图建模示意图" id="heterogeneous-graph">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 稠密区域背景 -->
        <mxCell id="dense_region_bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F8FF;strokeColor=#87CEEB;strokeWidth=2;dashed=1;dashPattern=8 8;opacity=30" vertex="1" parent="1">
          <mxGeometry x="60" y="60" width="200" height="140" as="geometry" />
        </mxCell>

        <mxCell id="dense_region_label" value="稠密区" style="text;html=1;strokeColor=none;fillColor=#F0F8FF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontColor=#4682B4;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="70" y="70" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 稀疏区域背景 -->
        <mxCell id="sparse_region_bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#DAA520;strokeWidth=2;dashed=1;dashPattern=8 8;opacity=30" vertex="1" parent="1">
          <mxGeometry x="300" y="80" width="80" height="80" as="geometry" />
        </mxCell>

        <mxCell id="sparse_region_label" value="稀疏区" style="text;html=1;strokeColor=none;fillColor=#FFF8DC;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontColor=#B8860B;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="310" y="90" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 稠密区标签节点 (4个) -->
        <mxCell id="tag1" value="T1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE4E1;strokeColor=#DC143C;fontColor=#8B0000;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="90" y="100" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="tag2" value="T2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE4E1;strokeColor=#DC143C;fontColor=#8B0000;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="90" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="tag3" value="T3" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE4E1;strokeColor=#DC143C;fontColor=#8B0000;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="120" y="160" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="tag4" value="T4" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE4E1;strokeColor=#DC143C;fontColor=#8B0000;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="210" y="150" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 稀疏区标签节点 (1个) -->
        <mxCell id="tag5" value="T5" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE4E1;strokeColor=#DC143C;fontColor=#8B0000;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="330" y="120" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点 (2个) -->
        <mxCell id="antenna1" value="A1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E6F3FF;strokeColor=#4A90E2;fontColor=#2E5C8A;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="220" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="antenna2" value="A2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E6F3FF;strokeColor=#4A90E2;fontColor=#2E5C8A;fontSize=10;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="280" y="220" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 新的有向标签连接拓扑结构 -->
        <!-- T1 → T3：从T1指向T3的有向连接 -->
        <mxCell id="tt1" value="" style="endArrow=classic;html=1;strokeColor=#D3D3D3;strokeWidth=1;arrowSize=6" edge="1" parent="1" source="tag1" target="tag3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="102" y="125" as="sourcePoint" />
            <mxPoint x="132" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T5 → T2：从T5指向T2的有向连接 -->
        <mxCell id="tt2" value="" style="endArrow=classic;html=1;strokeColor=#D3D3D3;strokeWidth=1;arrowSize=6" edge="1" parent="1" source="tag5" target="tag2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="132" as="sourcePoint" />
            <mxPoint x="225" y="102" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T5 → T4：从T5指向T4的有向连接 -->
        <mxCell id="tt3" value="" style="endArrow=classic;html=1;strokeColor=#D3D3D3;strokeWidth=1;arrowSize=6" edge="1" parent="1" source="tag5" target="tag4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="132" as="sourcePoint" />
            <mxPoint x="235" y="162" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 标签-天线连接 (每个标签都与每个天线相连，使用浅色虚线) -->
        <!-- T1的连接线带权重标注作为示例 -->
        <mxCell id="ta1_1" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag1" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="102" y="125" as="sourcePoint" />
            <mxPoint x="162" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta1_1_weight" value="w=0.8" style="text;html=1;strokeColor=none;fillColor=#FFFFFF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=8;fontColor=#666666;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="115" y="155" width="25" height="12" as="geometry" />
        </mxCell>

        <mxCell id="ta1_2" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag1" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="115" y="112" as="sourcePoint" />
            <mxPoint x="292" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta1_2_weight" value="w=0.3" style="text;html=1;strokeColor=none;fillColor=#FFFFFF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=8;fontColor=#666666;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="190" y="155" width="25" height="12" as="geometry" />
        </mxCell>

        <mxCell id="ta2_1" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag2" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="212" y="115" as="sourcePoint" />
            <mxPoint x="162" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta2_2" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag2" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="225" y="102" as="sourcePoint" />
            <mxPoint x="292" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta3_1" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag3" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="132" y="185" as="sourcePoint" />
            <mxPoint x="162" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta3_2" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag3" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="145" y="172" as="sourcePoint" />
            <mxPoint x="292" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta4_1" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag4" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="222" y="175" as="sourcePoint" />
            <mxPoint x="162" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta4_2" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag4" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="235" y="162" as="sourcePoint" />
            <mxPoint x="292" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta5_1" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag5" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="342" y="145" as="sourcePoint" />
            <mxPoint x="162" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta5_2" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="tag5" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="355" y="132" as="sourcePoint" />
            <mxPoint x="292" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例 (移除外边框，平铺排列，居中显示) -->
        <mxCell id="legend_title" value="图例" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#333333" vertex="1" parent="1">
          <mxGeometry x="200" y="280" width="40" height="20" as="geometry" />
        </mxCell>

        <!-- 标签节点图例 -->
        <mxCell id="legend_tag" value="T" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE4E1;strokeColor=#DC143C;fontColor=#8B0000;fontSize=8;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="80" y="305" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend_tag_text" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#333333" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="50" height="20" as="geometry" />
        </mxCell>

        <!-- 天线节点图例 -->
        <mxCell id="legend_antenna" value="A" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E6F3FF;strokeColor=#4A90E2;fontColor=#2E5C8A;fontSize=8;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="160" y="305" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend_antenna_text" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#333333" vertex="1" parent="1">
          <mxGeometry x="180" y="300" width="50" height="20" as="geometry" />
        </mxCell>

        <!-- 标签连接图例 (有向连接) -->
        <mxCell id="legend_tt_line" value="" style="endArrow=classic;html=1;strokeColor=#D3D3D3;strokeWidth=1;arrowSize=4" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="312" as="sourcePoint" />
            <mxPoint x="255" y="312" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend_tt_text" value="有向标签连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#333333" vertex="1" parent="1">
          <mxGeometry x="260" y="300" width="70" height="20" as="geometry" />
        </mxCell>

        <!-- 标签-天线连接图例 -->
        <mxCell id="legend_ta_line" value="" style="endArrow=none;html=1;strokeColor=#E0E0E0;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="312" as="sourcePoint" />
            <mxPoint x="335" y="312" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend_ta_text" value="标签-天线连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#333333" vertex="1" parent="1">
          <mxGeometry x="340" y="300" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 权重标注图例 -->
        <mxCell id="legend_weight" value="w=0.8" style="text;html=1;strokeColor=none;fillColor=#FFFFFF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=8;fontColor=#666666;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="430" y="305" width="20" height="10" as="geometry" />
        </mxCell>

        <mxCell id="legend_weight_text" value="权重标注" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#333333" vertex="1" parent="1">
          <mxGeometry x="455" y="300" width="50" height="20" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
