<mxfile host="app.diagrams.net" modified="2025-01-11T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="异构图建模示意图" id="heterogeneous-graph">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <mxCell id="tag1" value="T1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#DAA520;fontColor=#8B4513;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="120" width="32" height="32" as="geometry" />
        </mxCell>

        <mxCell id="tag2" value="T2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#DAA520;fontColor=#8B4513;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="350" y="100" width="32" height="32" as="geometry" />
        </mxCell>

        <mxCell id="tag3" value="T3" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#DAA520;fontColor=#8B4513;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="280" width="32" height="32" as="geometry" />
        </mxCell>

        <mxCell id="tag4" value="T4" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#DAA520;fontColor=#8B4513;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="420" y="260" width="32" height="32" as="geometry" />
        </mxCell>

        <mxCell id="antenna1" value="A1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E6F3FF;strokeColor=#4A90E2;fontColor=#2E5C8A;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="200" width="32" height="32" as="geometry" />
        </mxCell>

        <mxCell id="antenna2" value="A2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E6F3FF;strokeColor=#4A90E2;fontColor=#2E5C8A;fontSize=11;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="180" width="32" height="32" as="geometry" />
        </mxCell>

        <mxCell id="tt1" value="" style="endArrow=none;html=1;strokeColor=#D3D3D3;strokeWidth=1;dashed=1;dashPattern=4 4" edge="1" parent="1" source="tag1" target="tag2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="130" as="sourcePoint" />
            <mxPoint x="350" y="110" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tt2" value="" style="endArrow=none;html=1;strokeColor=#D3D3D3;strokeWidth=1;dashed=1;dashPattern=4 4" edge="1" parent="1" source="tag2" target="tag4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="130" as="sourcePoint" />
            <mxPoint x="430" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tt3" value="" style="endArrow=none;html=1;strokeColor=#D3D3D3;strokeWidth=1;dashed=1;dashPattern=4 4" edge="1" parent="1" source="tag3" target="tag4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="290" as="sourcePoint" />
            <mxPoint x="420" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tt4" value="" style="endArrow=none;html=1;strokeColor=#D3D3D3;strokeWidth=1;dashed=1;dashPattern=4 4" edge="1" parent="1" source="tag1" target="tag3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="170" y="150" as="sourcePoint" />
            <mxPoint x="210" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta1" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag1" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="160" y="140" as="sourcePoint" />
            <mxPoint x="120" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta1_weight" value="w=0.9" style="text;html=1;strokeColor=none;fillColor=#FFFFFF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontColor=#2E5C8A;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="125" y="165" width="30" height="15" as="geometry" />
        </mxCell>

        <mxCell id="ta2" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag1" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="130" as="sourcePoint" />
            <mxPoint x="480" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta2_weight" value="w=0.3" style="text;html=1;strokeColor=none;fillColor=#FFFFFF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontColor=#2E5C8A;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="315" y="145" width="30" height="15" as="geometry" />
        </mxCell>

        <mxCell id="ta3" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag2" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="120" as="sourcePoint" />
            <mxPoint x="130" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta4" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag2" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="120" as="sourcePoint" />
            <mxPoint x="490" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta5" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag3" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="290" as="sourcePoint" />
            <mxPoint x="120" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta6" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag3" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="290" as="sourcePoint" />
            <mxPoint x="480" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta7" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag4" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="270" as="sourcePoint" />
            <mxPoint x="130" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta8" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1" source="tag4" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="270" as="sourcePoint" />
            <mxPoint x="490" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="aa1" value="" style="endArrow=none;html=1;strokeColor=#B3D9FF;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1" source="antenna1" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="210" as="sourcePoint" />
            <mxPoint x="480" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="adaptive_range_t1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#FFE4B5;strokeWidth=1;dashed=1;dashPattern=4 4;opacity=60" vertex="1" parent="1">
          <mxGeometry x="120" y="90" width="120" height="120" as="geometry" />
        </mxCell>

        <mxCell id="adaptive_label_t1" value="K=3自适应连接" style="text;html=1;strokeColor=none;fillColor=#FFFEF7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontColor=#8B4513;fontStyle=1;strokeColor=#FFE4B5;strokeWidth=1" vertex="1" parent="1">
          <mxGeometry x="130" y="80" width="80" height="15" as="geometry" />
        </mxCell>

        <mxCell id="potential_connection1" value="" style="endArrow=none;html=1;strokeColor=#F0F0F0;strokeWidth=1;dashed=1;dashPattern=2 2;opacity=50" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="136" as="sourcePoint" />
            <mxPoint x="250" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="potential_connection2" value="" style="endArrow=none;html=1;strokeColor=#F0F0F0;strokeWidth=1;dashed=1;dashPattern=2 2;opacity=50" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="170" y="150" as="sourcePoint" />
            <mxPoint x="200" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="potential_label" value="潜在连接" style="text;html=1;strokeColor=none;fillColor=#FAFAFA;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=8;fontColor=#CCCCCC;fontStyle=2" vertex="1" parent="1">
          <mxGeometry x="210" y="170" width="50" height="12" as="geometry" />
        </mxCell>



        <mxCell id="weight_formula" value="边权重公式：w = 1/(1+d²)" style="text;html=1;strokeColor=none;fillColor=#FFFEF7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontColor=#8B4513;fontStyle=1;strokeColor=#FFE4B5;strokeWidth=1" vertex="1" parent="1">
          <mxGeometry x="40" y="350" width="180" height="25" as="geometry" />
        </mxCell>

        <mxCell id="legend_bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAFBFC;strokeColor=#E1E4E8;strokeWidth=1" vertex="1" parent="1">
          <mxGeometry x="620" y="70" width="90" height="120" as="geometry" />
        </mxCell>

        <mxCell id="legend_title" value="图例" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="645" y="75" width="40" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend_tag" value="T" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#DAA520;fontColor=#8B4513;fontSize=7;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="625" y="95" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="legend_tag_text" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="640" y="95" width="50" height="12" as="geometry" />
        </mxCell>

        <mxCell id="legend_antenna" value="A" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E6F3FF;strokeColor=#4A90E2;fontColor=#2E5C8A;fontSize=7;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="625" y="110" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="legend_antenna_text" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="640" y="110" width="50" height="12" as="geometry" />
        </mxCell>

        <mxCell id="legend_separator1" value="" style="endArrow=none;html=1;strokeColor=#E1E4E8;strokeWidth=1" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="625" y="125" as="sourcePoint" />
            <mxPoint x="705" y="125" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend_tt_line" value="" style="endArrow=none;html=1;strokeColor=#D3D3D3;strokeWidth=1;dashed=1;dashPattern=4 4" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="625" y="130" as="sourcePoint" />
            <mxPoint x="635" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend_tt_text" value="标签连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="640" y="125" width="50" height="10" as="geometry" />
        </mxCell>

        <mxCell id="legend_ta_line" value="" style="endArrow=none;html=1;strokeColor=#4A90E2;strokeWidth=1" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="625" y="140" as="sourcePoint" />
            <mxPoint x="635" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend_ta_text" value="标签-天线" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="640" y="135" width="50" height="10" as="geometry" />
        </mxCell>

        <mxCell id="legend_aa_line" value="" style="endArrow=none;html=1;strokeColor=#B3D9FF;strokeWidth=1;dashed=1;dashPattern=3 3" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="625" y="150" as="sourcePoint" />
            <mxPoint x="635" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend_aa_text" value="天线连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="640" y="145" width="50" height="10" as="geometry" />
        </mxCell>

        <mxCell id="legend_separator2" value="" style="endArrow=none;html=1;strokeColor=#E1E4E8;strokeWidth=1" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="625" y="160" as="sourcePoint" />
            <mxPoint x="705" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="weight_display_example" value="w=0.8" style="text;html=1;strokeColor=none;fillColor=#FFFFFF;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=7;fontColor=#2E5C8A;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="625" y="165" width="20" height="10" as="geometry" />
        </mxCell>

        <mxCell id="weight_display_text" value="权重标注" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="650" y="160" width="50" height="10" as="geometry" />
        </mxCell>

        <mxCell id="adaptive_range_legend" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#FFE4B5;strokeWidth=1;dashed=1;dashPattern=4 4;opacity=60" vertex="1" parent="1">
          <mxGeometry x="625" y="175" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="adaptive_range_text" value="自适应范围" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#586069" vertex="1" parent="1">
          <mxGeometry x="640" y="170" width="60" height="10" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
